name: CI

on:
  push:
    branches: [ master, develop ]
  pull_request:
    branches: [ master, develop ]

jobs:
  test:
    name: Test & Coverage
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest
        
    - name: Install dependencies
      run: bun install
      
    - name: Run tests with coverage
      run: bun test --coverage --coverage-reporter=lcov
      
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v5
      with:
        files: ./coverage/lcov.info
        fail_ci_if_error: true
        token: ${{ secrets.CODECOV_TOKEN }}
        verbose: true

  lint:
    name: Lint
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest
        
    - name: Install dependencies
      run: bun install --frozen-lockfile
      
    - name: Run ESLint
      run: bun run lint
      
    - name: Check formatting
      run: bun run format:check

  build:
    name: Build & Type Check
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest
        
    - name: Install dependencies
      run: bun install --frozen-lockfile
      
    - name: Type check
      run: bun run type-check
      
    - name: Build project
      run: bun run build

  security:
    name: Security Audit
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest
        
    - name: Install dependencies
      run: bun install --frozen-lockfile
      
    - name: Run security audit
      run: bun audit

  coverage-check:
    name: Coverage Check
    runs-on: ubuntu-latest
    needs: test
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest
        
    - name: Install dependencies
      run: bun install --frozen-lockfile
      
    - name: Generate coverage report
      run: bun test --coverage --coverage-reporter=json-summary
      
    - name: Check coverage threshold
      run: |
        COVERAGE=$(cat coverage/coverage-summary.json | jq '.total.lines.pct')
        echo "Current coverage: $COVERAGE%"
        if (( $(echo "$COVERAGE < 80" | bc -l) )); then
          echo "Coverage $COVERAGE% is below minimum threshold of 80%"
          exit 1
        fi
        echo "Coverage check passed!"

  changed-tests:
    name: Run Changed Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Bun
      uses: oven-sh/setup-bun@v1
      with:
        bun-version: latest

    - name: Install dependencies
      run: bun install --frozen-lockfile

    - name: Get changed test files
      id: changed
      run: |
        git fetch origin ${{ github.event.pull_request.base.ref }} --depth=1
        CHANGED=$(git diff --name-only origin/${{ github.event.pull_request.base.ref }}...HEAD | grep '^test/.*\\.test\\.ts$' || true)
        echo "tests=$CHANGED" >> "$GITHUB_OUTPUT"

    - name: Run changed tests
      if: steps.changed.outputs.tests != ''
      run: bun test ${{ steps.changed.outputs.tests }}

    - name: No tests changed
      if: steps.changed.outputs.tests == ''
      run: echo "No test changes detected"
